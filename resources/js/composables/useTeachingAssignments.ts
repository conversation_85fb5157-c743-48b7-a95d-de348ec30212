import { ref, computed } from 'vue';
import { router } from '@inertiajs/vue3';
import { useApiRequest } from './useApiRequest';
import { TEACHING_ASSIGNMENT_ROUTES } from '@/constants';
import type {
  TeachingAssignment,
  AvailableLecturer,
  ScheduleConflict,
  AssignmentFilters,
  PaginatedResponse,
  AssignLecturerRequest,
  ConflictCheckRequest,
  ExportRequest,
  ApiResponse,
  ConflictCheckResponse
} from '@/types/TeachingAssignment';

export function useTeachingAssignments() {
  const { makeRequest, loading, error } = useApiRequest();

  // State
  const assignments = ref<PaginatedResponse<TeachingAssignment> | null>(null);
  const availableLecturers = ref<AvailableLecturer[]>([]);
  const conflicts = ref<ScheduleConflict[]>([]);
  const currentFilters = ref<AssignmentFilters>({});

  // Computed
  const hasAssignments = computed(() => assignments.value?.data.length > 0);
  const totalAssignments = computed(() => assignments.value?.meta.total ?? 0);
  const hasConflicts = computed(() => conflicts.value.length > 0);

  // Methods
  const fetchAssignments = async (filters: AssignmentFilters = {}) => {
    try {
      currentFilters.value = filters;
      const response = await makeRequest<PaginatedResponse<TeachingAssignment>>(
        'get',
        route(TEACHING_ASSIGNMENT_ROUTES.API.INDEX),
        { params: filters }
      );
      
      if (response.success && response.data) {
        assignments.value = response.data;
      }
      
      return response;
    } catch (err) {
      console.error('Failed to fetch teaching assignments:', err);
      throw err;
    }
  };

  const assignLecturer = async (request: AssignLecturerRequest) => {
    try {
      const response = await makeRequest<ApiResponse>(
        'post',
        route(TEACHING_ASSIGNMENT_ROUTES.API.ASSIGN),
        { data: request }
      );

      if (response.success) {
        // Refresh assignments after successful assignment
        await fetchAssignments(currentFilters.value);
      }

      return response;
    } catch (err) {
      console.error('Failed to assign lecturer:', err);
      throw err;
    }
  };

  const unassignLecturer = async (courseOfferingId: number) => {
    try {
      const response = await makeRequest<ApiResponse>(
        'delete',
        route(TEACHING_ASSIGNMENT_ROUTES.API.UNASSIGN, { courseOfferingId })
      );

      if (response.success) {
        // Refresh assignments after successful unassignment
        await fetchAssignments(currentFilters.value);
      }

      return response;
    } catch (err) {
      console.error('Failed to unassign lecturer:', err);
      throw err;
    }
  };

  const fetchAvailableLecturers = async (courseOfferingId: number, filters: Partial<AssignmentFilters> = {}) => {
    try {
      const response = await makeRequest<{ data: AvailableLecturer[] }>(
        'get',
        route(TEACHING_ASSIGNMENT_ROUTES.API.AVAILABLE_LECTURERS, { courseOfferingId }),
        { params: filters }
      );

      if (response.success && response.data) {
        availableLecturers.value = response.data.data;
      }

      return response;
    } catch (err) {
      console.error('Failed to fetch available lecturers:', err);
      throw err;
    }
  };

  const checkConflicts = async (request: ConflictCheckRequest) => {
    try {
      const response = await makeRequest<ConflictCheckResponse>(
        'post',
        route(TEACHING_ASSIGNMENT_ROUTES.API.CHECK_CONFLICTS),
        { data: request }
      );

      if (response.success && response.data) {
        conflicts.value = response.data.conflicts;
      }

      return response;
    } catch (err) {
      console.error('Failed to check conflicts:', err);
      throw err;
    }
  };

  const exportAssignments = async (request: ExportRequest) => {
    try {
      const response = await makeRequest<Blob>(
        'post',
        route(TEACHING_ASSIGNMENT_ROUTES.API.EXPORT),
        { 
          data: request,
          responseType: 'blob'
        }
      );

      if (response.success && response.data) {
        // Create download link
        const url = window.URL.createObjectURL(response.data);
        const link = document.createElement('a');
        link.href = url;
        link.download = `teaching-assignments-${new Date().toISOString().split('T')[0]}.${request.format}`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      }

      return response;
    } catch (err) {
      console.error('Failed to export assignments:', err);
      throw err;
    }
  };

  const clearConflicts = () => {
    conflicts.value = [];
  };

  const clearAvailableLecturers = () => {
    availableLecturers.value = [];
  };

  const refreshAssignments = () => {
    return fetchAssignments(currentFilters.value);
  };

  // Navigation helpers
  const goToAssignments = (filters?: AssignmentFilters) => {
    router.visit(route(TEACHING_ASSIGNMENT_ROUTES.INDEX), {
      data: filters,
      preserveState: true,
      preserveScroll: true
    });
  };

  return {
    // State
    assignments: readonly(assignments),
    availableLecturers: readonly(availableLecturers),
    conflicts: readonly(conflicts),
    currentFilters: readonly(currentFilters),
    loading,
    error,

    // Computed
    hasAssignments,
    totalAssignments,
    hasConflicts,

    // Methods
    fetchAssignments,
    assignLecturer,
    unassignLecturer,
    fetchAvailableLecturers,
    checkConflicts,
    exportAssignments,
    clearConflicts,
    clearAvailableLecturers,
    refreshAssignments,
    goToAssignments
  };
}
