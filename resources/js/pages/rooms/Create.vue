<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import {
    DAYS_OF_WEEK,
    getDaysOfWeekOptions,
    getRoomStatusOptions,
    getRoomTypeOptions,
    roomFormSchema,
    type RoomFormData,
} from '@/schemas/room';
import { systemRoutes } from '@/utils/routes';
import { Head, router } from '@inertiajs/vue3';
import { toTypedSchema } from '@vee-validate/zod';
import { ArrowLeft, Building2, Clock, Info, Settings } from 'lucide-vue-next';
import { computed } from 'vue';
import { useForm, useField } from 'vee-validate';
import { toast } from 'vue-sonner';

// Schema for vee-validate
const validationSchema = toTypedSchema(roomFormSchema);

// Use vee-validate form
const { handleSubmit, errors, meta, values } = useForm({
    validationSchema,
    initialValues: {
        name: '',
        code: '',
        building: '',
        floor: '',
        type: 'classroom',
        capacity: 30,
        status: 'available',
        is_bookable: true,
        requires_approval: false,
        available_from: '',
        available_until: '',
        blocked_days: [] as (typeof DAYS_OF_WEEK[number])[],
        description: '',
        usage_guidelines: '',
        booking_notes: '',
    } as RoomFormData,
});

// Define fields with vee-validate
const { value: name, errorMessage: nameError } = useField<string>('name');
const { value: code, errorMessage: codeError } = useField<string>('code');
const { value: building, errorMessage: buildingError } = useField<string>('building');
const { value: floor, errorMessage: floorError } = useField<string>('floor');
const { value: type, errorMessage: typeError } = useField<string>('type');
const { value: capacity, errorMessage: capacityError } = useField<number>('capacity');
const { value: status, errorMessage: statusError } = useField<string>('status');
const { value: isBookable, errorMessage: isBookableError } = useField<boolean>('is_bookable');
const { value: requiresApproval, errorMessage: requiresApprovalError } = useField<boolean>('requires_approval');
const { value: availableFrom, errorMessage: availableFromError } = useField<string>('available_from');
const { value: availableUntil, errorMessage: availableUntilError } = useField<string>('available_until');
const { value: blockedDays, errorMessage: blockedDaysError } = useField<(typeof DAYS_OF_WEEK[number])[]>('blocked_days');
const { value: description, errorMessage: descriptionError } = useField<string>('description');
const { value: usageGuidelines, errorMessage: usageGuidelinesError } = useField<string>('usage_guidelines');
const { value: bookingNotes, errorMessage: bookingNotesError } = useField<string>('booking_notes');

// Options
const roomTypeOptions = getRoomTypeOptions();
const roomStatusOptions = getRoomStatusOptions();
const daysOfWeekOptions = getDaysOfWeekOptions();

// Handle blocked days selection
const toggleBlockedDay = (day: string) => {
    const currentDays = [...(blockedDays.value || [])];
    const index = currentDays.indexOf(day as (typeof DAYS_OF_WEEK)[number]);

    if (index > -1) {
        currentDays.splice(index, 1);
    } else {
        currentDays.push(day as (typeof DAYS_OF_WEEK)[number]);
    }

    blockedDays.value = currentDays;
};

// Check if day is blocked
const isDayBlocked = (day: string) => {
    return (blockedDays.value || []).includes(day as (typeof DAYS_OF_WEEK)[number]);
};

// Form submission with vee-validate
const onSubmit = handleSubmit((values) => {
    // Submit to server using Inertia
    router.post(systemRoutes.rooms.store(), values, {
        onSuccess: () => {
            toast.success('Room created successfully!');
            router.visit(systemRoutes.rooms.index());
        },
        onError: (errors) => {
            // Display server-side validation errors
            const firstErrorKey = Object.keys(errors)[0];
            const firstError = errors[firstErrorKey];
            
            if (firstError) {
                toast.error(Array.isArray(firstError) ? firstError[0] : firstError);
            } else {
                toast.error('Failed to create room. Please check the form for errors.');
            }
        },
    });
});

// Navigation
const goBack = () => {
    router.visit(systemRoutes.rooms.index());
};

// Computed properties
const isFormValid = computed(() => {
    return meta.value.valid && meta.value.dirty;
});
</script>

<template>
    <Head title="Create Room" />

    <!-- Header -->
    <div class="flex items-center gap-4">
        <Button variant="ghost" size="icon" @click="goBack" class="h-8 w-8">
            <ArrowLeft class="h-4 w-4" />
        </Button>
        <div>
            <h1 class="text-2xl font-semibold">Create New Room</h1>
            <p class="text-muted-foreground">Add a new room to the system</p>
        </div>
    </div>

    <form @submit.prevent="onSubmit" class="space-y-6">
        <div class="grid grid-cols-1 gap-6 lg:grid-cols-2">
            <!-- Basic Information Card -->
            <Card>
                <CardHeader>
                    <CardTitle class="flex items-center gap-2">
                        <Building2 class="h-5 w-5" />
                        Basic Information
                    </CardTitle>
                    <CardDescription> Enter the basic details for the room </CardDescription>
                </CardHeader>
                <CardContent class="space-y-4">
                    <div class="space-y-2">
                        <Label for="name">Room Name</Label>
                        <Input
                            id="name"
                            v-model="name"
                            placeholder="e.g., Lecture Theatre 1, Lab A"
                            required
                            :class="{ 'border-red-500': nameError }"
                        />
                        <p v-if="nameError" class="text-sm text-red-500">
                            {{ nameError }}
                        </p>
                    </div>

                    <div class="space-y-2">
                        <Label for="code">Room Code</Label>
                        <Input
                            id="code"
                            v-model="form.code"
                            placeholder="e.g., LT1, LAB-A, CR101"
                            required
                            :class="{ 'border-red-500': hasFieldError('code') }"
                        />
                        <p v-if="getFieldError('code')" class="text-sm text-red-500">
                            {{ getFieldError('code') }}
                        </p>
                    </div>

                    <div class="grid grid-cols-2 gap-4">
                        <div class="space-y-2">
                            <Label for="building">Building</Label>
                            <Input
                                id="building"
                                v-model="form.building"
                                placeholder="e.g., Main Building"
                                required
                                :class="{ 'border-red-500': hasFieldError('building') }"
                            />
                            <p v-if="getFieldError('building')" class="text-sm text-red-500">
                                {{ getFieldError('building') }}
                            </p>
                        </div>

                        <div class="space-y-2">
                            <Label for="floor">Floor</Label>
                            <Input
                                id="floor"
                                v-model="form.floor"
                                placeholder="e.g., Ground, 1st, 2nd"
                                required
                                :class="{ 'border-red-500': hasFieldError('floor') }"
                            />
                            <p v-if="getFieldError('floor')" class="text-sm text-red-500">
                                {{ getFieldError('floor') }}
                            </p>
                        </div>
                    </div>

                    <div class="grid grid-cols-2 gap-4">
                        <div class="space-y-2">
                            <Label for="type">Room Type</Label>
                            <Select v-model="form.type">
                                <SelectTrigger>
                                    <SelectValue placeholder="Select room type" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem v-for="option in roomTypeOptions" :key="option.value" :value="option.value">
                                        {{ option.label }}
                                    </SelectItem>
                                </SelectContent>
                            </Select>
                            <p v-if="getFieldError('type')" class="text-sm text-red-500">
                                {{ getFieldError('type') }}
                            </p>
                        </div>

                        <div class="space-y-2">
                            <Label for="capacity">Capacity</Label>
                            <Input
                                id="capacity"
                                v-model.number="form.capacity"
                                type="number"
                                min="1"
                                max="10000"
                                placeholder="30"
                                required
                                :class="{ 'border-red-500': hasFieldError('capacity') }"
                            />
                            <p v-if="getFieldError('capacity')" class="text-sm text-red-500">
                                {{ getFieldError('capacity') }}
                            </p>
                        </div>
                    </div>
                </CardContent>
            </Card>

            <!-- Status & Settings Card -->
            <Card>
                <CardHeader>
                    <CardTitle class="flex items-center gap-2">
                        <Settings class="h-5 w-5" />
                        Status & Settings
                    </CardTitle>
                    <CardDescription> Configure room status and booking settings </CardDescription>
                </CardHeader>
                <CardContent class="space-y-4">
                    <div class="space-y-2">
                        <Label for="status">Status</Label>
                        <Select v-model="form.status">
                            <SelectTrigger>
                                <SelectValue placeholder="Select status" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem v-for="option in roomStatusOptions" :key="option.value" :value="option.value">
                                    {{ option.label }}
                                </SelectItem>
                            </SelectContent>
                        </Select>
                        <p v-if="getFieldError('status')" class="text-sm text-red-500">
                            {{ getFieldError('status') }}
                        </p>
                    </div>

                    <div class="space-y-4">
                        <div class="flex items-center space-x-2">
                            <Checkbox id="is_bookable" v-model:checked="form.is_bookable" />
                            <Label for="is_bookable" class="text-sm font-medium"> Room is bookable </Label>
                        </div>

                        <div class="flex items-center space-x-2">
                            <Checkbox id="requires_approval" v-model:checked="form.requires_approval" />
                            <Label for="requires_approval" class="text-sm font-medium"> Requires approval for booking </Label>
                        </div>
                    </div>
                </CardContent>
            </Card>
        </div>

        <!-- Availability Settings Card -->
        <Card>
            <CardHeader>
                <CardTitle class="flex items-center gap-2">
                    <Clock class="h-5 w-5" />
                    Availability Settings
                </CardTitle>
                <CardDescription> Configure when the room is available for booking </CardDescription>
            </CardHeader>
            <CardContent class="space-y-4">
                <div class="grid grid-cols-2 gap-4">
                    <div class="space-y-2">
                        <Label for="available_from">Available From (Time)</Label>
                        <Input
                            id="available_from"
                            v-model="form.available_from"
                            type="time"
                            step="1"
                            placeholder="08:00:00"
                            :class="{ 'border-red-500': hasFieldError('available_from') }"
                        />
                        <p class="text-muted-foreground text-xs">Leave empty for no time restriction</p>
                        <p v-if="getFieldError('available_from')" class="text-sm text-red-500">
                            {{ getFieldError('available_from') }}
                        </p>
                    </div>

                    <div class="space-y-2">
                        <Label for="available_until">Available Until (Time)</Label>
                        <Input
                            id="available_until"
                            v-model="form.available_until"
                            type="time"
                            step="1"
                            placeholder="18:00:00"
                            :class="{ 'border-red-500': hasFieldError('available_until') }"
                        />
                        <p class="text-muted-foreground text-xs">Leave empty for no time restriction</p>
                        <p v-if="getFieldError('available_until')" class="text-sm text-red-500">
                            {{ getFieldError('available_until') }}
                        </p>
                    </div>
                </div>

                <div class="space-y-2">
                    <Label>Blocked Days</Label>
                    <div class="grid grid-cols-7 gap-2">
                        <div v-for="day in daysOfWeekOptions" :key="day.value" class="flex items-center space-x-2">
                            <Checkbox
                                :id="`day-${day.value}`"
                                :checked="isDayBlocked(day.value)"
                                @update:checked="() => toggleBlockedDay(day.value)"
                            />
                            <Label :for="`day-${day.value}`" class="text-sm">
                                {{ day.label.substring(0, 3) }}
                            </Label>
                        </div>
                    </div>
                    <p class="text-muted-foreground text-xs">Select days when the room is not available for booking</p>
                </div>
            </CardContent>
        </Card>

        <!-- Additional Information Card -->
        <Card>
            <CardHeader>
                <CardTitle class="flex items-center gap-2">
                    <Info class="h-5 w-5" />
                    Additional Information
                </CardTitle>
                <CardDescription> Optional details about the room </CardDescription>
            </CardHeader>
            <CardContent class="space-y-4">
                <div class="space-y-2">
                    <Label for="description">Description</Label>
                    <Textarea
                        id="description"
                        v-model="form.description"
                        placeholder="Brief description of the room..."
                        rows="3"
                        :class="{ 'border-red-500': hasFieldError('description') }"
                    />
                    <p v-if="getFieldError('description')" class="text-sm text-red-500">
                        {{ getFieldError('description') }}
                    </p>
                </div>

                <div class="space-y-2">
                    <Label for="usage_guidelines">Usage Guidelines</Label>
                    <Textarea
                        id="usage_guidelines"
                        v-model="form.usage_guidelines"
                        placeholder="Guidelines for using this room..."
                        rows="3"
                        :class="{ 'border-red-500': hasFieldError('usage_guidelines') }"
                    />
                    <p v-if="getFieldError('usage_guidelines')" class="text-sm text-red-500">
                        {{ getFieldError('usage_guidelines') }}
                    </p>
                </div>

                <div class="space-y-2">
                    <Label for="booking_notes">Booking Notes</Label>
                    <Textarea
                        id="booking_notes"
                        v-model="form.booking_notes"
                        placeholder="Important notes for booking this room..."
                        rows="3"
                        :class="{ 'border-red-500': hasFieldError('booking_notes') }"
                    />
                    <p v-if="getFieldError('booking_notes')" class="text-sm text-red-500">
                        {{ getFieldError('booking_notes') }}
                    </p>
                </div>
            </CardContent>
        </Card>

        <!-- Form Actions -->
        <div class="flex items-center justify-end gap-4">
            <Button type="button" variant="outline" @click="goBack"> Cancel </Button>
            <Button type="submit" :disabled="form.processing || !isFormValid" class="min-w-[120px]">
                <span v-if="form.processing">Creating...</span>
                <span v-else>Create Room</span>
            </Button>
        </div>
    </form>
</template>
