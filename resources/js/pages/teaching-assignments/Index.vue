<template>
  <AppLayout>
    <template #header>
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-semibold text-gray-900">Teaching Assignments</h1>
          <p class="mt-1 text-sm text-gray-600">
            Manage lecturer assignments to course offerings
          </p>
        </div>
        <div class="flex items-center space-x-3">
          <!-- Export Button -->
          <button
            type="button"
            class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            @click="showExportModal = true"
          >
            <Icon name="download" class="w-4 h-4 mr-2" />
            Export
          </button>
          
          <!-- Refresh Button -->
          <button
            type="button"
            class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            @click="refreshData"
            :disabled="loading"
          >
            <Icon name="refresh-cw" class="w-4 h-4 mr-2" :class="{ 'animate-spin': loading }" />
            Refresh
          </button>
        </div>
      </div>
    </template>

    <div class="space-y-6">
      <!-- Filters Section -->
      <div class="bg-white shadow rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Filters</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <!-- Semester Filter -->
          <div>
            <label for="semester" class="block text-sm font-medium text-gray-700 mb-1">
              Semester
            </label>
            <select
              id="semester"
              v-model="filters.semester_id"
              class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              @change="applyFilters"
            >
              <option value="">All Semesters</option>
              <option
                v-for="semester in semesters"
                :key="semester.id"
                :value="semester.id"
              >
                {{ semester.name }}
              </option>
            </select>
          </div>

          <!-- Campus Filter -->
          <div>
            <label for="campus" class="block text-sm font-medium text-gray-700 mb-1">
              Campus
            </label>
            <select
              id="campus"
              v-model="filters.campus_id"
              class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              @change="applyFilters"
            >
              <option value="">All Campuses</option>
              <option
                v-for="campus in campuses"
                :key="campus.id"
                :value="campus.id"
              >
                {{ campus.name }}
              </option>
            </select>
          </div>

          <!-- Assignment Status Filter -->
          <div>
            <label for="assignment-status" class="block text-sm font-medium text-gray-700 mb-1">
              Assignment Status
            </label>
            <select
              id="assignment-status"
              v-model="filters.assignment_status"
              class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              @change="applyFilters"
            >
              <option value="">All Statuses</option>
              <option value="assigned">Assigned</option>
              <option value="unassigned">Unassigned</option>
              <option value="urgent">Urgent</option>
            </select>
          </div>

          <!-- Search -->
          <div>
            <label for="search" class="block text-sm font-medium text-gray-700 mb-1">
              Search
            </label>
            <DebouncedInput
              id="search"
              v-model="filters.search"
              placeholder="Search units, lecturers..."
              class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              @update:model-value="applyFilters"
            />
          </div>
        </div>

        <!-- Additional Filters Row -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-4">
          <!-- Faculty Filter -->
          <div>
            <label for="faculty" class="block text-sm font-medium text-gray-700 mb-1">
              Faculty
            </label>
            <input
              id="faculty"
              v-model="filters.faculty"
              type="text"
              placeholder="Enter faculty name"
              class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              @input="debouncedApplyFilters"
            />
          </div>

          <!-- Department Filter -->
          <div>
            <label for="department" class="block text-sm font-medium text-gray-700 mb-1">
              Department
            </label>
            <input
              id="department"
              v-model="filters.department"
              type="text"
              placeholder="Enter department name"
              class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              @input="debouncedApplyFilters"
            />
          </div>

          <!-- Clear Filters -->
          <div class="flex items-end">
            <button
              type="button"
              class="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              @click="clearFilters"
            >
              <Icon name="x" class="w-4 h-4 mr-2" />
              Clear Filters
            </button>
          </div>
        </div>
      </div>

      <!-- Summary Stats -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div class="bg-white overflow-hidden shadow rounded-lg">
          <div class="p-5">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <Icon name="book-open" class="h-6 w-6 text-gray-400" />
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">Total Courses</dt>
                  <dd class="text-lg font-medium text-gray-900">{{ totalAssignments }}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
          <div class="p-5">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <Icon name="user-check" class="h-6 w-6 text-green-400" />
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">Assigned</dt>
                  <dd class="text-lg font-medium text-gray-900">{{ assignedCount }}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
          <div class="p-5">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <Icon name="user-x" class="h-6 w-6 text-red-400" />
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">Unassigned</dt>
                  <dd class="text-lg font-medium text-gray-900">{{ unassignedCount }}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
          <div class="p-5">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <Icon name="alert-triangle" class="h-6 w-6 text-yellow-400" />
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">Urgent</dt>
                  <dd class="text-lg font-medium text-gray-900">{{ urgentCount }}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Assignments Table -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">Course Assignments</h3>
        </div>

        <div class="p-6">
          <TeachingAssignmentsTable
            :assignments="assignments?.data || []"
            :meta="assignments?.meta"
            :loading="loading"
            :total-assignments="totalAssignments"
            @assign-lecturer="handleAssignLecturer"
            @unassign-lecturer="handleUnassignLecturer"
            @view-details="handleViewDetails"
            @page-change="handlePageChange"
          />
        </div>
      </div>
    </div>

    <!-- Assignment Modal -->
    <AssignmentModal
      v-if="selectedAssignment"
      :course-offering="selectedAssignment"
      :is-open="showAssignmentModal"
      @close="closeAssignmentModal"
      @assigned="handleLecturerAssigned"
      @unassigned="handleLecturerUnassigned"
    />

    <!-- Export Modal -->
    <!-- TODO: Implement export modal in Task 11 -->
  </AppLayout>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { debounce } from 'lodash-es';
import AppLayout from '@/layouts/AppLayout.vue';
import Icon from '@/components/Icon.vue';
import DebouncedInput from '@/components/DebouncedInput.vue';
import TeachingAssignmentsTable from '@/components/TeachingAssignmentsTable.vue';
import AssignmentModal from '@/components/AssignmentModal.vue';
import { useTeachingAssignments } from '@/composables/useTeachingAssignments';
import { useFilterOptions } from '@/composables/useFilterOptions';
import type { AssignmentFilters, TeachingAssignment } from '@/types/TeachingAssignment';

// Props
interface Props {
  filters?: Partial<AssignmentFilters>;
}

const props = withDefaults(defineProps<Props>(), {
  filters: () => ({})
});

// Composables
const {
  assignments,
  loading,
  totalAssignments,
  fetchAssignments
} = useTeachingAssignments();

const {
  semesters,
  campuses,
  faculties,
  departments
} = useFilterOptions();

// State
const filters = ref<AssignmentFilters>({
  ...props.filters
});
const showExportModal = ref(false);
const showAssignmentModal = ref(false);
const selectedAssignment = ref<TeachingAssignment | null>(null);

// Computed
const assignedCount = computed(() => 
  assignments.value?.data.filter(a => a.assignment_status === 'assigned').length ?? 0
);

const unassignedCount = computed(() => 
  assignments.value?.data.filter(a => a.assignment_status === 'unassigned').length ?? 0
);

const urgentCount = computed(() => 
  assignments.value?.data.filter(a => a.assignment_status === 'urgent').length ?? 0
);

// Methods
const applyFilters = () => {
  fetchAssignments(filters.value);
};

const debouncedApplyFilters = debounce(applyFilters, 300);

const clearFilters = () => {
  filters.value = {};
  applyFilters();
};

const refreshData = () => {
  fetchAssignments(filters.value);
};

const handleAssignLecturer = (assignment: TeachingAssignment) => {
  selectedAssignment.value = assignment;
  showAssignmentModal.value = true;
};

const handleUnassignLecturer = (assignment: TeachingAssignment) => {
  selectedAssignment.value = assignment;
  showAssignmentModal.value = true;
};

const handleViewDetails = (assignment: TeachingAssignment) => {
  // TODO: Open details modal or navigate to details page
  console.log('View details for:', assignment);
};

const handlePageChange = (page: number) => {
  filters.value.per_page = filters.value.per_page || 15;
  const newFilters = { ...filters.value, page };
  fetchAssignments(newFilters);
};

const closeAssignmentModal = () => {
  showAssignmentModal.value = false;
  selectedAssignment.value = null;
};

const handleLecturerAssigned = (lecturerId: number) => {
  // Refresh the assignments list to show the updated assignment
  refreshData();
  closeAssignmentModal();
};

const handleLecturerUnassigned = () => {
  // Refresh the assignments list to show the updated assignment
  refreshData();
  closeAssignmentModal();
};

// Lifecycle
onMounted(() => {
  fetchAssignments(filters.value);
});
</script>
