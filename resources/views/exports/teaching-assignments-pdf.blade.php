<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Teaching Assignments Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            margin: 20px;
            color: #333;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #4472C4;
            padding-bottom: 15px;
        }
        
        .header h1 {
            color: #4472C4;
            margin: 0;
            font-size: 24px;
        }
        
        .header .subtitle {
            color: #666;
            margin: 5px 0;
            font-size: 14px;
        }
        
        .filters {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #4472C4;
        }
        
        .filters h3 {
            margin: 0 0 10px 0;
            color: #4472C4;
            font-size: 14px;
        }
        
        .filter-item {
            display: inline-block;
            margin-right: 20px;
            margin-bottom: 5px;
        }
        
        .filter-label {
            font-weight: bold;
            color: #555;
        }
        
        .summary {
            background-color: #e8f4fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border: 1px solid #4472C4;
        }
        
        .summary h3 {
            margin: 0 0 10px 0;
            color: #4472C4;
            font-size: 16px;
        }
        
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
        }
        
        .summary-item {
            text-align: center;
        }
        
        .summary-number {
            font-size: 24px;
            font-weight: bold;
            color: #4472C4;
            display: block;
        }
        
        .summary-label {
            font-size: 11px;
            color: #666;
            margin-top: 5px;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            font-size: 10px;
        }
        
        th {
            background-color: #4472C4;
            color: white;
            padding: 8px 4px;
            text-align: left;
            font-weight: bold;
            border: 1px solid #ddd;
        }
        
        td {
            padding: 6px 4px;
            border: 1px solid #ddd;
            vertical-align: top;
        }
        
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        tr:hover {
            background-color: #f0f8ff;
        }
        
        .status-assigned {
            background-color: #d4edda;
            color: #155724;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 9px;
            font-weight: bold;
        }
        
        .status-unassigned {
            background-color: #f8d7da;
            color: #721c24;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 9px;
            font-weight: bold;
        }
        
        .status-urgent {
            background-color: #fff3cd;
            color: #856404;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 9px;
            font-weight: bold;
        }
        
        .priority-high {
            color: #dc3545;
            font-weight: bold;
        }
        
        .priority-urgent {
            color: #fd7e14;
            font-weight: bold;
        }
        
        .priority-normal {
            color: #28a745;
        }
        
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 10px;
        }
        
        .page-break {
            page-break-before: always;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Teaching Assignments Report</h1>
        <div class="subtitle">Generated on {{ now()->format('F j, Y \a\t g:i A') }}</div>
    </div>

    @if(!empty($filters))
    <div class="filters">
        <h3>Applied Filters</h3>
        @if(!empty($filters['semester_id']))
            <div class="filter-item">
                <span class="filter-label">Semester:</span> {{ $filters['semester_name'] ?? $filters['semester_id'] }}
            </div>
        @endif
        @if(!empty($filters['campus_id']))
            <div class="filter-item">
                <span class="filter-label">Campus:</span> {{ $filters['campus_name'] ?? $filters['campus_id'] }}
            </div>
        @endif
        @if(!empty($filters['faculty']))
            <div class="filter-item">
                <span class="filter-label">Faculty:</span> {{ $filters['faculty'] }}
            </div>
        @endif
        @if(!empty($filters['department']))
            <div class="filter-item">
                <span class="filter-label">Department:</span> {{ $filters['department'] }}
            </div>
        @endif
        @if(!empty($filters['assignment_status']))
            <div class="filter-item">
                <span class="filter-label">Status:</span> {{ ucfirst(str_replace('_', ' ', $filters['assignment_status'])) }}
            </div>
        @endif
        @if(!empty($filters['search']))
            <div class="filter-item">
                <span class="filter-label">Search:</span> {{ $filters['search'] }}
            </div>
        @endif
    </div>
    @endif

    <div class="summary">
        <h3>Summary Statistics</h3>
        <div class="summary-grid">
            <div class="summary-item">
                <span class="summary-number">{{ $assignments->count() }}</span>
                <div class="summary-label">Total Course Offerings</div>
            </div>
            <div class="summary-item">
                <span class="summary-number">{{ $assignments->whereNotNull('lecture_id')->count() }}</span>
                <div class="summary-label">Assigned Courses</div>
            </div>
            <div class="summary-item">
                <span class="summary-number">{{ $assignments->whereNull('lecture_id')->count() }}</span>
                <div class="summary-label">Unassigned Courses</div>
            </div>
            <div class="summary-item">
                <span class="summary-number">{{ $assignments->pluck('lecture_id')->filter()->unique()->count() }}</span>
                <div class="summary-label">Unique Lecturers</div>
            </div>
        </div>
    </div>

    <table>
        <thead>
            <tr>
                <th>Semester</th>
                <th>Unit Code</th>
                <th>Unit Name</th>
                <th>Section</th>
                <th>Lecturer</th>
                <th>Department</th>
                <th>Schedule</th>
                <th>Location</th>
                <th>Enrollment</th>
                <th>Status</th>
                <th>Priority</th>
            </tr>
        </thead>
        <tbody>
            @foreach($assignments as $assignment)
            <tr>
                <td>{{ $assignment->semester?->name ?? 'N/A' }}</td>
                <td><strong>{{ $assignment->curriculumUnit?->unit?->code ?? 'N/A' }}</strong></td>
                <td>{{ $assignment->curriculumUnit?->unit?->name ?? 'N/A' }}</td>
                <td>{{ $assignment->section_code ?? 'N/A' }}</td>
                <td>{{ $assignment->lecture?->display_name ?? 'Unassigned' }}</td>
                <td>{{ $assignment->lecture?->department ?? 'N/A' }}</td>
                <td>
                    @if($assignment->schedule_days)
                        {{ implode(', ', $assignment->schedule_days) }}<br>
                        {{ $assignment->schedule_time_start?->format('H:i') ?? 'N/A' }} - 
                        {{ $assignment->schedule_time_end?->format('H:i') ?? 'N/A' }}
                    @else
                        N/A
                    @endif
                </td>
                <td>{{ $assignment->location ?? 'N/A' }}</td>
                <td>
                    {{ $assignment->current_enrollment ?? 0 }}/{{ $assignment->max_capacity ?? 0 }}
                    @if($assignment->current_waitlist)
                        <br><small>Waitlist: {{ $assignment->current_waitlist }}</small>
                    @endif
                </td>
                <td>
                    @php
                        $status = $assignment->getInstructorAssignmentStatus();
                    @endphp
                    <span class="status-{{ $status }}">
                        {{ $assignment->getInstructorAssignmentStatusLabel() }}
                    </span>
                </td>
                <td>
                    @php
                        $priority = $assignment->getAssignmentPriority();
                    @endphp
                    <span class="priority-{{ $priority }}">
                        {{ ucfirst($priority) }}
                    </span>
                </td>
            </tr>
            @endforeach
        </tbody>
    </table>

    <div class="footer">
        <p>This report contains {{ $assignments->count() }} course offering(s) | Generated by Teaching Assignment System</p>
    </div>
</body>
</html>
